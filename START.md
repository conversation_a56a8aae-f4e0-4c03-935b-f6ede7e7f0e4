# 🚀 快速启动指南

## 启动方式

### 方法1: 使用启动脚本（推荐）
```bash
./start.sh
```

### 方法2: 直接运行
```bash
python3 main.py
```

## 📱 访问地址

- **应用界面**: http://127.0.0.1:7869

## 🔧 环境变量配置

应用支持通过 `.env` 文件进行配置：

### 默认配置 (.env)
```
AIBIAO_API_KEY=sk-00be7e20409a4d43adb75771d6310ba5mnqp03or
APP_HOST=127.0.0.1
APP_PORT=7866
APP_DEBUG=true
API_BASE_URL=https://aibiao.cn
API_TIMEOUT=30
LOG_LEVEL=DEBUG
```

### 配置说明
- **AIBIAO_API_KEY**: Aibiao API密钥
- **APP_HOST**: 应用监听地址
- **APP_PORT**: 应用端口
- **APP_DEBUG**: 调试模式
- **API_BASE_URL**: API基础URL
- **API_TIMEOUT**: API超时时间（秒）
- **LOG_LEVEL**: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## 🔑 使用步骤

1. **获取API Key**: 访问 https://aibiao.cn/public/api-key
2. **设置API Key**: 在应用界面输入您的真实API Key
3. **开始使用**:
   - 输入数据文件的URL（支持CSV、Excel文件的网络地址）
   - 系统直接将URL传递给Aibiao AI进行分析
   - 与AI助手对话讨论数据问题
   - 获取智能图表建议

## ⚠️ 注意事项

- 需要真实的 Aibiao API Key
- 所有API调用会消耗配额
- 支持CSV、Excel文件格式
- 请妥善保管API Key

## 🆘 故障排除

如果遇到问题，请查看 `SOLUTION_GUIDE.md` 文件。