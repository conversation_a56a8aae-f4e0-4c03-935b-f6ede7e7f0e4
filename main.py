#!/usr/bin/env python3
"""
Aibiao AI 数据分析助手 - 支持环境变量配置
自动从环境变量读取API Key和配置
"""

import os
import gradio as gr
import requests
import time
import logging
import json

class ConfigAssistant:
    """支持环境变量配置的AI助手"""
    
    def __init__(self):
        # 从环境变量读取配置
        self.api_key = os.getenv('AIBIAO_API_KEY', 'sk-00be7e20409a4d43adb75771d6310ba5mnqp03or')
        self.api_base_url = os.getenv('API_BASE_URL', 'https://aibiao.cn')
        self.api_timeout = int(os.getenv('API_TIMEOUT', '30'))
        self.app_host = os.getenv('APP_HOST', '127.0.0.1')
        self.app_port = int(os.getenv('APP_PORT', '7866'))
        self.app_debug = os.getenv('APP_DEBUG', 'true').lower() == 'true'
        
        # 日志级别配置
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.log_level = getattr(logging, log_level, logging.INFO)
        
        # 配置日志
        self._setup_logging()
        
        print(f"🔧 配置加载完成:")
        print(f"  - API Key: {'***' + self.api_key[-4:] if self.api_key else '未设置'}")
        print(f"  - API Base URL: {self.api_base_url}")
        print(f"  - App Host: {self.app_host}")
        print(f"  - App Port: {self.app_port}")
        print(f"  - Debug Mode: {self.app_debug}")
        print(f"  - Log Level: {logging.getLevelName(self.log_level)}")
    
    def _setup_logging(self):
        """配置日志系统"""
        # 创建日志记录器
        self.logger = logging.getLogger('ConfigAssistant')
        self.logger.setLevel(self.log_level)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(self.log_level)
            
            # 创建格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器到日志记录器
            self.logger.addHandler(console_handler)
    
    def _log_request_response(self, url, method='GET', headers=None, data=None, response=None, error=None):
        """记录API请求和响应的详细信息"""
        self.logger.debug("=" * 60)
        self.logger.debug("API Request/Response Details")
        self.logger.debug("=" * 60)
        
        # 记录请求信息
        self.logger.debug(f"Request Method: {method}")
        self.logger.debug(f"Request URL: {url}")
        self.logger.debug(f"Request Headers: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        
        if data:
            try:
                if isinstance(data, str):
                    # 尝试解析JSON
                    try:
                        parsed_data = json.loads(data)
                        self.logger.debug(f"Request Data: {json.dumps(parsed_data, indent=2, ensure_ascii=False)}")
                    except:
                        self.logger.debug(f"Request Data: {data}")
                else:
                    self.logger.debug(f"Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except Exception as e:
                self.logger.debug(f"Request Data (raw): {data}")
        
        # 记录响应信息
        if response:
            self.logger.debug(f"Response Status Code: {response.status_code}")
            self.logger.debug(f"Response Headers: {json.dumps(dict(response.headers), indent=2, ensure_ascii=False)}")
            
            try:
                response_text = response.text
                self.logger.debug(f"Response Content Length: {len(response_text)} characters")
                
                # 检查是否是SSE响应
                if 'data: ' in response_text:
                    self.logger.debug("检测到SSE响应，开始解析...")
                    # 解析前几行SSE数据，修复编码问题
                    sse_lines = response_text.split('\n')[:10]  # 只显示前10行
                    for i, line in enumerate(sse_lines):
                        if line.strip():
                            try:
                                # 尝试修复编码问题
                                if line.startswith('data: '):
                                    data_part = line[6:]
                                    # 尝试解析JSON来验证编码
                                    try:
                                        json_data = json.loads(data_part)
                                        # 如果解析成功，使用ensure_ascii=False来显示中文
                                        fixed_line = f"data: {json.dumps(json_data, ensure_ascii=False)}"
                                        self.logger.debug(f"SSE Line {i+1}: {fixed_line[:300]}...")
                                    except:
                                        # 如果JSON解析失败，直接显示
                                        self.logger.debug(f"SSE Line {i+1}: {line[:300]}...")
                                else:
                                    self.logger.debug(f"SSE Line {i+1}: {line[:300]}...")
                            except Exception as e:
                                self.logger.debug(f"SSE Line {i+1}: [编码错误] {line[:100]}...")
                    self.logger.debug("...(SSE响应已截断，详细解析见SSE处理器)")
                else:
                    # 尝试解析JSON响应
                    try:
                        response_json = response.json()
                        self.logger.debug(f"Response JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                    except:
                        # 如果不是JSON，记录原始文本（截断长文本）
                        if len(response_text) > 1000:
                            self.logger.debug(f"Response Text (truncated): {response_text[:1000]}...")
                        else:
                            self.logger.debug(f"Response Text: {response_text}")
            except Exception as e:
                self.logger.debug(f"Error reading response: {str(e)}")
        
        # 记录错误信息
        if error:
            self.logger.debug(f"Error: {str(error)}")
        
        self.logger.debug("=" * 60)
        
    def update_api_key(self, new_key):
        """更新API Key"""
        self.api_key = new_key
        if new_key:
            if self.test_api_key():
                return "✅ API Key 验证成功"
            else:
                return "❌ API Key 无效，请检查"
        else:
            return "⚠️ 请输入API Key"
    
    def test_api_key(self):
        """测试API Key是否有效"""
        if not self.api_key:
            return False
            
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            url = f"{self.api_base_url}/api/enterprise/external/api-key-status"
            print(f"🔍 测试API Key: {url}")
            
            # 发送请求
            response = requests.get(url, headers=headers, timeout=self.api_timeout)
            print(f"📡 API Key测试响应: {response.status_code}")
            
            # 记录详细的请求/响应信息
            self._log_request_response(
                url=url,
                method='GET',
                headers=headers,
                response=response
            )
            
            return response.status_code == 200
        except Exception as e:
            print(f"❌ API Key测试失败: {str(e)}")
            self.logger.debug(f"API Key测试异常: {str(e)}")
            return False
    
    def get_headers(self):
        """获取请求头"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        return headers
    
    def _fix_encoding(self, text):
        """修复文本编码问题"""
        if isinstance(text, str):
            try:
                # 尝试将文本编码为UTF-8，然后解码回来
                return text.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')
            except:
                return text
        elif isinstance(text, dict):
            # 如果是字典，递归修复所有字符串值
            return {k: self._fix_encoding(v) for k, v in text.items()}
        elif isinstance(text, list):
            # 如果是列表，递归修复所有元素
            return [self._fix_encoding(item) for item in text]
        return text
    
    def _fix_unicode_escapes(self, text):
        """修复Unicode转义字符"""
        if isinstance(text, str):
            try:
                # 使用unicode-escape编解码器来修复Unicode转义字符
                return text.encode('utf-8').decode('unicode-escape').encode('utf-8').decode('utf-8')
            except:
                try:
                    # 如果上面的方法失败，尝试直接使用unicode-escape
                    return text.encode('raw_unicode_escape').decode('utf-8')
                except:
                    return text
        return text
    
    def _parse_sse_response(self, response_text):
        """解析Server-Sent Events (SSE)响应"""
        lines = response_text.split('\n')
        chat_id = None
        final_result = None
        
        for line in lines:
            line = line.strip()
            if line.startswith('data: '):
                data_str = line[6:]  # 移除 'data: ' 前缀
                if data_str:
                    try:
                        # 直接解析JSON
                        data = json.loads(data_str)
                        
                        # 修复Unicode转义字符
                        data = self._fix_unicode_escapes_recursive(data)
                        
                        # 使用ensure_ascii=False来正确显示中文
                        debug_data = json.dumps(data, ensure_ascii=False, indent=2)
                        self.logger.debug(f"解析SSE数据: {debug_data}")
                        
                        # 如果是聊天详情，提取结果
                        if 'result' in data and data['result']:
                            final_result = data['result']
                        # 如果是新聊天响应，提取ID
                        elif 'id' in data:
                            chat_id = data
                        # 如果是状态信息，记录但不覆盖结果
                        elif 'status' in data:
                            self.logger.debug(f"处理状态: {data['status']}")
                    except json.JSONDecodeError as e:
                        self.logger.debug(f"解析JSON失败: {e}, 数据: {data_str}")
                        continue
        
        # 如果有最终结果，直接返回结果字符串
        if final_result:
            return final_result
        
        # 如果没有最终结果但有聊天ID，返回聊天ID字典
        if chat_id:
            return chat_id
        
        return None
    
    def _fix_unicode_escapes_recursive(self, data):
        """递归修复Unicode转义字符"""
        if isinstance(data, str):
            try:
                # 处理Unicode转义序列
                import re
                def replace_unicode_escapes(match):
                    hex_str = match.group(1)
                    return chr(int(hex_str, 16))
                
                # 匹配 \uXXXX 格式的Unicode转义序列
                pattern = re.compile(r'\\u([0-9a-fA-F]{4})')
                return pattern.sub(replace_unicode_escapes, data)
            except:
                return data
        elif isinstance(data, dict):
            return {k: self._fix_unicode_escapes_recursive(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._fix_unicode_escapes_recursive(item) for item in data]
        else:
            return data
    
    def analyze_data_with_ai(self, file_url, analysis_prompt):
        """使用AI分析数据"""
        if not self.api_key:
            return "❌ 请先设置有效的API Key"
        
        if not file_url:
            return "❌ 请先输入文件URL"
        
        try:
            print(f"📖 开始处理文件URL: {file_url}")
            
            # 验证URL格式
            if not file_url.startswith(('http://', 'https://')):
                return "❌ 无效的URL格式，请使用http://或https://开头的链接"
            
            # 直接将文件URL传递给Aibiao API
            analysis_request = {
                "title": "AI数据分析",
                "prompt": f"""
作为AI数据分析专家，请分析以下数据并回答用户的问题。

## 文件URL
{file_url}

## 用户需求
{analysis_prompt}

请基于以上文件数据进行全面分析，并提供：
1. 数据洞察和发现
2. 具体的分析结果
3. 可视化建议
4. 后续分析建议

请用中文回答，结构清晰，分析要具体详细。
""",
                "fileUrls": [file_url],  # 直接传递文件URL给Aibiao
                "steps": ["数据理解", "需求分析", "AI分析", "结果生成"]
            }
            
            # 调用Aibiao API
            headers = self.get_headers()
            url = f"{self.api_base_url}/api/enterprise/external/new-chat"
            print(f"🚀 调用Aibiao API: {url}")
            print(f"📎 文件URL: {file_url}")
            
            response = requests.post(
                url,
                headers=headers,
                json=analysis_request,
                timeout=self.api_timeout
            )
            
            print(f"📥 API响应状态: {response.status_code}")
            
            # 记录详细的请求/响应信息
            self._log_request_response(
                url=url,
                method='POST',
                headers=headers,
                data=analysis_request,
                response=response
            )
            
            if response.status_code == 200:
                # 处理SSE响应
                result = self._parse_sse_response(response.text)
                if result:
                    print(f"✅ API响应成功")
                    
                    # 如果是字典且包含id，则是聊天创建响应
                    if isinstance(result, dict) and 'id' in result:
                        chat_id = result['id']
                        print(f"💬 聊天ID: {chat_id}")
                        
                        # 等待处理完成
                        print("⏳ 等待AI处理...")
                        time.sleep(5)  # 增加等待时间
                        
                        # 获取分析结果
                        detail_url = f"{self.api_base_url}/api/enterprise/external/chat-detail/{chat_id}"
                        print(f"📡 获取分析结果: {detail_url}")
                        
                        chat_response = requests.get(
                            detail_url,
                            headers=headers,
                            timeout=self.api_timeout
                        )
                        
                        print(f"📥 分析结果响应: {chat_response.status_code}")
                        
                        # 记录获取分析结果的详细请求/响应信息
                        self._log_request_response(
                            url=detail_url,
                            method='GET',
                            headers=headers,
                            response=chat_response
                        )
                        
                        if chat_response.status_code == 200:
                            # 处理聊天详情的SSE响应
                            chat_result = self._parse_sse_response(chat_response.text)
                            if chat_result:
                                analysis_result = chat_result
                                print(f"✅ 获取分析结果成功")
                            else:
                                analysis_result = "分析中，请稍后重试..."
                        else:
                            self.logger.debug(f"获取分析结果失败，状态码: {chat_response.status_code}")
                            analysis_result = f"获取分析结果失败: {chat_response.status_code}"
                    else:
                        # 如果直接返回了结果字符串
                        analysis_result = result
                        print(f"✅ 获取分析结果成功")
                else:
                    self.logger.debug("无法解析SSE响应")
                    analysis_result = "无法解析API响应"
            else:
                self.logger.debug(f"API调用失败，状态码: {response.status_code}")
                analysis_result = f"❌ API调用失败: {response.status_code}"
                
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {str(e)}")
            self.logger.debug(f"分析过程中出现异常: {str(e)}")
            analysis_result = f"❌ 分析过程中出现错误: {str(e)}"
        
        return analysis_result
    
    def chat_with_ai(self, message, history):
        """与AI对话"""
        if not self.api_key:
            return history + [["用户", "❌ 请先设置有效的API Key"]]
        
        try:
            chat_request = {
                "chatId": "conversation",
                "message": message,
                "fileUrls": []  # 对话功能不需要文件
            }
            
            headers = self.get_headers()
            url = f"{self.api_base_url}/api/enterprise/external/chat"
            
            response = requests.post(
                url,
                headers=headers,
                json=chat_request,
                timeout=self.api_timeout
            )
            
            # 记录详细的请求/响应信息
            self._log_request_response(
                url=url,
                method='POST',
                headers=headers,
                data=chat_request,
                response=response
            )
            
            if response.status_code == 200:
                # 处理SSE响应
                result = self._parse_sse_response(response.text)
                if result:
                    # SSE解析器现在直接返回结果字符串或None
                    ai_response = result if isinstance(result, str) else "抱歉，我没有得到有效的回复。"
                    return history + [["用户", message], ["AI助手", ai_response]]
                else:
                    self.logger.debug("无法解析对话SSE响应")
                    return history + [["用户", message], ["AI助手", "❌ 对话响应解析失败"]]
            else:
                error_msg = f"❌ 对话失败: {response.status_code}"
                self.logger.debug(f"对话API调用失败，状态码: {response.status_code}")
                return history + [["用户", message], ["AI助手", error_msg]]
                
        except Exception as e:
            self.logger.debug(f"对话过程中出现异常: {str(e)}")
            return history + [["用户", message], ["AI助手", f"❌ 对话错误: {str(e)}"]]

# 创建界面
def create_interface():
    """创建Gradio界面"""
    assistant = ConfigAssistant()
    
    with gr.Blocks(title="Aibiao AI 数据分析助手") as demo:
        
        # 标题
        gr.HTML(f"""
        <div style="text-align: center; margin-bottom: 30px;">
            <h1>🤖 Aibiao AI 数据分析助手</h1>
            <p>基于AI的智能数据分析平台 - 支持环境变量配置</p>
            <p><small>当前API Key: ***{assistant.api_key[-4:] if assistant.api_key else '未设置'}</small></p>
        </div>
        """)
        
        # API Key设置
        with gr.Row():
            api_key_input = gr.Textbox(
                label="🔑 Aibiao API Key",
                placeholder="请输入您的 aibiao.cn API Key",
                value=assistant.api_key,
                type="password"
            )
            update_key_btn = gr.Button("设置 API Key")
            key_status = gr.Textbox(label="状态", interactive=False)
        
        update_key_btn.click(
            fn=assistant.update_api_key,
            inputs=api_key_input,
            outputs=key_status
        )
        
        # 功能标签页
        with gr.Tabs():
            # 数据分析
            with gr.TabItem("📊 AI数据分析"):
                gr.Markdown("### 输入数据文件URL，让AI为您分析")
                
                with gr.Row():
                    with gr.Column():
                        # 使用文件URL输入
                        file_url_input = gr.Textbox(
                            label="🌐 文件URL",
                            placeholder="请输入文件的URL地址（支持CSV、Excel文件的网络地址）",
                            lines=2
                        )
                        analysis_prompt = gr.Textbox(
                            label="💭 分析需求",
                            placeholder="请描述您希望AI分析什么？例如：分析销售趋势、发现异常值、比较不同类别等...",
                            lines=3
                        )
                        analyze_btn = gr.Button("🚀 开始AI分析")
                    
                    with gr.Column():
                        analysis_result = gr.Markdown(
                            label="📈 分析结果",
                            value="请输入文件URL并输入分析需求"
                        )
                
                analyze_btn.click(
                    fn=assistant.analyze_data_with_ai,
                    inputs=[file_url_input, analysis_prompt],
                    outputs=analysis_result
                )
            
            # AI对话
            with gr.TabItem("💬 AI对话"):
                gr.Markdown("### 与AI数据分析助手对话")
                
                chatbot = gr.Chatbot(label="🤖 AI助手", height=400)
                msg_input = gr.Textbox(label="💬 输入您的问题")
                send_btn = gr.Button("发送")
                
                def respond(message, history):
                    return "", assistant.chat_with_ai(message, history)
                
                msg_input.submit(
                    fn=respond,
                    inputs=[msg_input, chatbot],
                    outputs=[msg_input, chatbot]
                )
                
                send_btn.click(
                    fn=respond,
                    inputs=[msg_input, chatbot],
                    outputs=[msg_input, chatbot]
                )
        
        # 使用说明
        gr.HTML("""
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
            <h3>📖 使用说明</h3>
            <ol>
                <li>获取API Key: 访问 <a href="https://aibiao.cn/public/api-key" target="_blank">https://aibiao.cn/public/api-key</a></li>
                <li>在上方输入您的API Key并点击"设置 API Key"</li>
                <li>使用AI数据分析功能输入文件URL并描述分析需求</li>
                <li>系统将直接把文件URL传递给Aibiao AI进行分析</li>
                <li>与AI助手对话获取更多分析建议</li>
            </ol>
            <p><strong>注意:</strong> 所有API调用都会消耗您的配额，请合理使用。</p>
            <p><strong>提示:</strong> 支持的文件格式：CSV、Excel (.xlsx, .xls)。</p>
            <p><strong>🔧 配置:</strong> 可通过 .env 文件配置环境变量。</p>
        </div>
        """)
    
    return demo

# 加载环境变量
def load_env_file():
    """加载环境变量文件"""
    env_file = '.env'
    if os.path.exists(env_file):
        print(f"📝 加载环境变量文件: {env_file}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    print(f"  - {key}=***{value[-4:] if value.endswith('mnqp03or') else '已设置'}")
    else:
        print(f"⚠️  环境变量文件不存在: {env_file}")

# 启动应用
if __name__ == "__main__":
    # 加载环境变量
    load_env_file()
    
    # 创建应用
    assistant = ConfigAssistant()
    demo = create_interface()
    
    print(f"🚀 启动应用...")
    print(f"📍 访问地址: http://{assistant.app_host}:{assistant.app_port}")
    
    demo.launch(
        share=False,
        server_name=assistant.app_host,
        server_port=assistant.app_port,
        debug=assistant.app_debug
    )