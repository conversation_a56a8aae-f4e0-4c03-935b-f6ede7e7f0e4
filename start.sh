#!/bin/bash

# Aibiao AI 数据分析助手 - 支持环境变量配置的启动脚本

echo "🤖 启动 Aibiao AI 数据分析助手 (支持环境变量)"
echo "========================================"

# 1. 检查Python
echo "🔍 检查环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到 python3，请先安装"
    exit 1
fi

# 2. 安装依赖
echo "📦 检查依赖..."
python3 -c "import gradio" 2>/dev/null || {
    echo "📥 安装依赖..."
    pip3 install gradio==3.50.0 pandas requests
}

# 3. 检查环境变量文件
echo "📝 检查环境变量文件..."
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，创建默认配置..."
    cp .env.example .env
fi

# 4. 从环境变量读取端口配置
source .env 2>/dev/null || true
APP_PORT=${APP_PORT:-7866}
APP_HOST=${APP_HOST:-127.0.0.1}

echo "🔧 应用配置:"
echo "  - 主机: $APP_HOST"
echo "  - 端口: $APP_PORT"
echo "  - API Key: ***${AIBIAO_API_KEY: -4}"

# 5. 清理端口
echo "🔧 清理端口..."
lsof -ti:${APP_PORT} | xargs kill -9 2>/dev/null || true

sleep 2

# 6. 启动应用
echo "🚀 启动应用..."
echo "📍 访问地址: http://${APP_HOST}:${APP_PORT}"
echo "⚠️  注意: 请确保使用有效的 aibiao.cn API Key"
echo "📝 环境变量: 从 .env 文件加载配置"
echo "========================================"
python3 main.py

echo ""
echo "🛑 应用已停止"
echo "========================================"